"""
可视化与结果输出模块

实现滤波器设计结果的可视化和数据输出，包括：
- S参数曲线绘制
- 滤波器布局图生成
- 优化收敛历史
- 中文字体支持
- 数据导出功能

支持的图表类型：
- S参数幅度和相位
- 插入损耗和回波损耗
- 群延迟
- VSWR
- 滤波器物理布局
- 优化收敛曲线
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib import font_manager
import seaborn as sns
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
import os
from pathlib import Path

# 设置中文字体支持
try:
    from .font_config import CHINESE_FONT_AVAILABLE, setup_chinese_font
    _chinese_font_available = CHINESE_FONT_AVAILABLE
    print("使用自动检测的中文字体配置")
except ImportError:
    # 备用字体设置
    def setup_chinese_font():
        """备用字体设置"""
        import matplotlib.font_manager as fm

        # 获取系统可用字体
        available_fonts = [f.name for f in fm.fontManager.ttflist]

        # 中文字体优先级列表
        chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']

        # 查找可用的中文字体
        found_font = None
        for font in chinese_fonts:
            if font in available_fonts:
                found_font = font
                break

        if found_font:
            plt.rcParams['font.sans-serif'] = [found_font]
            plt.rcParams['font.family'] = 'sans-serif'
            plt.rcParams['axes.unicode_minus'] = False
            print(f"使用备用中文字体: {found_font}")
            return True
        else:
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            print("警告: 未找到中文字体，将使用英文标签")
            return False

    _chinese_font_available = setup_chinese_font()


class FilterVisualizer:
    """滤波器可视化类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化可视化器

        Args:
            config: 配置字典
        """
        self.config = config
        self.output_config = config.get('output', {})
        self.plot_config = self.output_config.get('plots', {})

        # 检查中文字体可用性
        self.chinese_available = _chinese_font_available

        # 设置绘图样式
        self._setup_plot_style()

        # 创建输出目录
        self.results_dir = Path(self.output_config.get('files', {}).get('results_dir', 'results'))
        self.results_dir.mkdir(exist_ok=True)

    def _get_label(self, chinese_text: str, english_text: str) -> str:
        """根据字体可用性返回合适的标签"""
        return chinese_text if self.chinese_available else english_text

    def _ensure_font_setting(self):
        """确保字体设置正确"""
        if self.chinese_available:
            # 重新应用字体配置
            try:
                from .font_config import setup_chinese_font
                setup_chinese_font()
            except ImportError:
                # 备用设置
                plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
                plt.rcParams['font.family'] = 'sans-serif'
                plt.rcParams['axes.unicode_minus'] = False
    
    def _setup_plot_style(self):
        """设置绘图样式"""
        # 设置seaborn样式
        sns.set_style("whitegrid")

        # 强制重新设置字体
        if self.chinese_available:
            # 找到可用的中文字体
            import matplotlib.font_manager as fm
            available_fonts = [f.name for f in fm.fontManager.ttflist]
            chinese_fonts = ['SimHei', 'Microsoft YaHei', 'SimSun', 'KaiTi']

            found_font = None
            for font in chinese_fonts:
                if font in available_fonts:
                    found_font = font
                    break

            if found_font:
                plt.rcParams['font.sans-serif'] = [found_font]
                plt.rcParams['font.family'] = 'sans-serif'
        else:
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            plt.rcParams['font.family'] = 'sans-serif'

        plt.rcParams['axes.unicode_minus'] = False

        # 设置字体
        font_size = self.plot_config.get('font_size', 12)
        plt.rcParams.update({
            'font.size': font_size,
            'axes.titlesize': font_size + 2,
            'axes.labelsize': font_size,
            'xtick.labelsize': font_size - 1,
            'ytick.labelsize': font_size - 1,
            'legend.fontsize': font_size - 1,
            'figure.titlesize': font_size + 4
        })

        # 设置图形尺寸和DPI
        figure_size = self.plot_config.get('figure_size', [10, 8])
        dpi = self.plot_config.get('dpi', 300)
        plt.rcParams['figure.figsize'] = figure_size
        plt.rcParams['figure.dpi'] = dpi
    
    def plot_s_parameters(self, metrics: Dict[str, np.ndarray],
                         save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制S参数曲线

        Args:
            metrics: 性能指标字典
            save_path: 保存路径

        Returns:
            matplotlib图形对象
        """
        self._ensure_font_setting()  # 确保字体设置正确
        frequency = metrics['frequency'] / 1e9  # 转换为GHz
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
        
        # S11幅度
        ax1.plot(frequency, metrics['s11_db'], 'b-', linewidth=2, label='S11')
        ax1.set_xlabel(self._get_label('频率 (GHz)', 'Frequency (GHz)'))
        ax1.set_ylabel(self._get_label('幅度 (dB)', 'Magnitude (dB)'))
        ax1.set_title(self._get_label('S11 - 输入反射系数', 'S11 - Input Reflection'))
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # S21幅度
        ax2.plot(frequency, metrics['s21_db'], 'r-', linewidth=2, label='S21')
        ax2.set_xlabel(self._get_label('频率 (GHz)', 'Frequency (GHz)'))
        ax2.set_ylabel(self._get_label('幅度 (dB)', 'Magnitude (dB)'))
        ax2.set_title(self._get_label('S21 - 前向传输系数', 'S21 - Forward Transmission'))
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # S11相位
        ax3.plot(frequency, metrics['s11_phase'], 'b--', linewidth=2,
                label=self._get_label('S11相位', 'S11 Phase'))
        ax3.set_xlabel(self._get_label('频率 (GHz)', 'Frequency (GHz)'))
        ax3.set_ylabel(self._get_label('相位 (度)', 'Phase (deg)'))
        ax3.set_title(self._get_label('S11相位', 'S11 Phase'))
        ax3.grid(True, alpha=0.3)
        ax3.legend()

        # S21相位
        ax4.plot(frequency, metrics['s21_phase'], 'r--', linewidth=2,
                label=self._get_label('S21相位', 'S21 Phase'))
        ax4.set_xlabel(self._get_label('频率 (GHz)', 'Frequency (GHz)'))
        ax4.set_ylabel(self._get_label('相位 (度)', 'Phase (deg)'))
        ax4.set_title(self._get_label('S21相位', 'S21 Phase'))
        ax4.grid(True, alpha=0.3)
        ax4.legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.plot_config.get('dpi', 300), bbox_inches='tight')
        
        return fig
    
    def plot_filter_response(self, metrics: Dict[str, np.ndarray],
                           specifications: Dict[str, Any],
                           save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制滤波器频率响应
        
        Args:
            metrics: 性能指标字典
            specifications: 设计指标
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        self._ensure_font_setting()  # 确保字体设置正确
        frequency = metrics['frequency'] / 1e9  # 转换为GHz
        
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 12))
        
        # 插入损耗
        ax1.plot(frequency, metrics['insertion_loss'], 'r-', linewidth=2,
                label=self._get_label('插入损耗', 'Insertion Loss'))

        # 添加设计指标线
        if 'passband' in specifications:
            passband = specifications['passband']
            freq_range = passband.get('frequency_range', [0.1, 1.5])
            max_il = passband.get('max_insertion_loss', 1.0)

            passband_req_label = self._get_label(f'通带要求 ≤ {max_il} dB', f'Passband Req ≤ {max_il} dB')
            passband_label = self._get_label('通带', 'Passband')
            ax1.axhline(y=max_il, color='g', linestyle='--', alpha=0.7, label=passband_req_label)
            ax1.axvspan(freq_range[0], freq_range[1], alpha=0.2, color='green', label=passband_label)

        if 'stopband' in specifications:
            stopband = specifications['stopband']
            freq_range = stopband.get('frequency_range', [2.0, 3.0])
            min_il = stopband.get('min_insertion_loss', 20.0)

            stopband_req_label = self._get_label(f'阻带要求 ≥ {min_il} dB', f'Stopband Req ≥ {min_il} dB')
            stopband_label = self._get_label('阻带', 'Stopband')
            ax1.axhline(y=min_il, color='orange', linestyle='--', alpha=0.7, label=stopband_req_label)
            ax1.axvspan(freq_range[0], freq_range[1], alpha=0.2, color='orange', label=stopband_label)

        ax1.set_xlabel(self._get_label('频率 (GHz)', 'Frequency (GHz)'))
        ax1.set_ylabel(self._get_label('插入损耗 (dB)', 'Insertion Loss (dB)'))
        ax1.set_title(self._get_label('滤波器插入损耗特性', 'Filter Insertion Loss'))
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.set_ylim(0, 50)
        
        # 回波损耗
        ax2.plot(frequency, metrics['return_loss'], 'b-', linewidth=2,
                label=self._get_label('回波损耗', 'Return Loss'))

        if 'passband' in specifications:
            passband = specifications['passband']
            freq_range = passband.get('frequency_range', [0.1, 1.5])
            min_rl = passband.get('min_return_loss', 10.0)

            passband_req_label = self._get_label(f'通带要求 ≥ {min_rl} dB', f'Passband Req ≥ {min_rl} dB')
            passband_label = self._get_label('通带', 'Passband')
            ax2.axhline(y=min_rl, color='g', linestyle='--', alpha=0.7, label=passband_req_label)
            ax2.axvspan(freq_range[0], freq_range[1], alpha=0.2, color='green', label=passband_label)

        ax2.set_xlabel(self._get_label('频率 (GHz)', 'Frequency (GHz)'))
        ax2.set_ylabel(self._get_label('回波损耗 (dB)', 'Return Loss (dB)'))
        ax2.set_title(self._get_label('滤波器回波损耗特性', 'Filter Return Loss'))
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.set_ylim(0, 40)

        # 群延迟
        ax3.plot(frequency, metrics['group_delay'] * 1e9, 'purple', linewidth=2,
                label=self._get_label('群延迟', 'Group Delay'))
        ax3.set_xlabel(self._get_label('频率 (GHz)', 'Frequency (GHz)'))
        ax3.set_ylabel(self._get_label('群延迟 (ns)', 'Group Delay (ns)'))
        ax3.set_title(self._get_label('滤波器群延迟特性', 'Filter Group Delay'))
        ax3.grid(True, alpha=0.3)
        ax3.legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.plot_config.get('dpi', 300), bbox_inches='tight')
        
        return fig
    
    def plot_vswr(self, metrics: Dict[str, np.ndarray],
                  save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制VSWR曲线
        
        Args:
            metrics: 性能指标字典
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        self._ensure_font_setting()  # 确保字体设置正确
        frequency = metrics['frequency'] / 1e9  # 转换为GHz
        
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))
        
        ax.plot(frequency, metrics['vswr'], 'g-', linewidth=2, label='VSWR')
        ax.axhline(y=2.0, color='r', linestyle='--', alpha=0.7, label='VSWR = 2')
        ax.axhline(y=1.5, color='orange', linestyle='--', alpha=0.7, label='VSWR = 1.5')

        ax.set_xlabel(self._get_label('频率 (GHz)', 'Frequency (GHz)'))
        ax.set_ylabel('VSWR')
        ax.set_title(self._get_label('电压驻波比 (VSWR)', 'Voltage Standing Wave Ratio (VSWR)'))
        ax.grid(True, alpha=0.3)
        ax.legend()
        ax.set_ylim(1, 10)
        ax.set_yscale('log')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.plot_config.get('dpi', 300), bbox_inches='tight')
        
        return fig
    
    def plot_optimization_history(self, convergence_history: List[float],
                                save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制优化收敛历史
        
        Args:
            convergence_history: 收敛历史
            save_path: 保存路径
            
        Returns:
            matplotlib图形对象
        """
        self._ensure_font_setting()  # 确保字体设置正确
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))
        
        generations = range(1, len(convergence_history) + 1)
        ax.plot(generations, convergence_history, 'b-', linewidth=2, marker='o', markersize=4)
        
        ax.set_xlabel(self._get_label('迭代代数', 'Generation'))
        ax.set_ylabel(self._get_label('目标函数值', 'Objective Function Value'))
        ax.set_title(self._get_label('优化收敛历史', 'Optimization Convergence History'))
        ax.grid(True, alpha=0.3)
        ax.set_yscale('log')

        # 添加最终值标注
        final_value = convergence_history[-1]
        final_label = self._get_label(f'最终值: {final_value:.2e}', f'Final: {final_value:.2e}')
        ax.annotate(final_label,
                   xy=(len(convergence_history), final_value),
                   xytext=(len(convergence_history)*0.7, final_value*10),
                   arrowprops=dict(arrowstyle='->', color='red'),
                   fontsize=10, color='red')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=self.plot_config.get('dpi', 300), bbox_inches='tight')
        
        return fig


class ResultsPlotter:
    """结果绘图器"""
    
    def __init__(self, visualizer: FilterVisualizer):
        """
        初始化结果绘图器
        
        Args:
            visualizer: 滤波器可视化器
        """
        self.visualizer = visualizer
    
    def generate_all_plots(self, metrics: Dict[str, np.ndarray],
                          layout: Any,
                          optimization_result: Any,
                          specifications: Dict[str, Any]) -> Dict[str, str]:
        """
        生成所有图表
        
        Args:
            metrics: 性能指标
            layout: 滤波器布局
            optimization_result: 优化结果
            specifications: 设计指标
            
        Returns:
            生成的图表文件路径字典
        """
        plot_paths = {}
        
        # S参数图
        s_params_path = self.visualizer.results_dir / "s_parameters.png"
        self.visualizer.plot_s_parameters(metrics, str(s_params_path))
        plot_paths['s_parameters'] = str(s_params_path)
        
        # 滤波器响应图
        response_path = self.visualizer.results_dir / "filter_response.png"
        self.visualizer.plot_filter_response(metrics, specifications, str(response_path))
        plot_paths['filter_response'] = str(response_path)
        
        # VSWR图
        vswr_path = self.visualizer.results_dir / "vswr.png"
        self.visualizer.plot_vswr(metrics, str(vswr_path))
        plot_paths['vswr'] = str(vswr_path)
        
        # 优化收敛历史
        if hasattr(optimization_result, 'convergence_history'):
            convergence_path = self.visualizer.results_dir / "optimization_convergence.png"
            self.visualizer.plot_optimization_history(
                optimization_result.convergence_history, str(convergence_path)
            )
            plot_paths['optimization_convergence'] = str(convergence_path)
        
        # 滤波器布局图
        if hasattr(layout, 'visualize_layout'):
            layout_path = self.visualizer.results_dir / "filter_layout.png"
            layout.visualize_layout(layout, str(layout_path))
            plot_paths['filter_layout'] = str(layout_path)
        
        return plot_paths
    
    def export_data(self, metrics: Dict[str, np.ndarray],
                   optimization_result: Any = None) -> str:
        """
        导出数据到CSV文件
        
        Args:
            metrics: 性能指标
            optimization_result: 优化结果
            
        Returns:
            CSV文件路径
        """
        # 创建DataFrame
        data = {
            '频率_GHz': metrics['frequency'] / 1e9,
            'S11_dB': metrics['s11_db'],
            'S21_dB': metrics['s21_db'],
            'S12_dB': metrics['s12_db'],
            'S22_dB': metrics['s22_db'],
            '插入损耗_dB': metrics['insertion_loss'],
            '回波损耗_dB': metrics['return_loss'],
            'S11相位_度': metrics['s11_phase'],
            'S21相位_度': metrics['s21_phase'],
            '群延迟_ns': metrics['group_delay'] * 1e9,
            'VSWR': metrics['vswr']
        }
        
        df = pd.DataFrame(data)
        
        # 保存CSV文件
        csv_path = self.visualizer.results_dir / "simulation_results.csv"
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        # 如果有优化结果，也保存
        if optimization_result:
            opt_data = {
                '代数': range(1, len(optimization_result.convergence_history) + 1),
                '目标函数值': optimization_result.convergence_history
            }
            opt_df = pd.DataFrame(opt_data)
            opt_csv_path = self.visualizer.results_dir / "optimization_history.csv"
            opt_df.to_csv(opt_csv_path, index=False, encoding='utf-8-sig')
        
        return str(csv_path)
